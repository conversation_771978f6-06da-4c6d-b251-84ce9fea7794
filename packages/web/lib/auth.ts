import { PrismaClient } from '@prisma/client';
import { betterAuth } from 'better-auth';
import { prismaAdapter } from 'better-auth/adapters/prisma';

// Create prisma client with error handling for build time
let prisma: PrismaClient | null = null;

// Skip database connection during build or when DATABASE_URL contains mock values
const isBuildTime =
  process.env.NODE_ENV === 'production' && !process.env.VERCEL && !process.env.RAILWAY_ENVIRONMENT;
const isMockDatabase =
  process.env.DATABASE_URL?.includes('build-mock-host') ||
  process.env.DATABASE_URL?.includes('localhost');
const hasValidDatabaseUrl =
  process.env.DATABASE_URL?.startsWith('postgresql://') && !isMockDatabase;

if (!isBuildTime && hasValidDatabaseUrl) {
  try {
    prisma = new PrismaClient();
  } catch (error) {
    console.warn('Failed to create Prisma client:', error);
  }
}

// GitHub profile type
interface GitHubProfile {
  id: number;
  login: string;
  name?: string;
  email?: string;
  avatar_url?: string;
}

// Callback parameters type
interface SignInParams {
  user: {
    id: string;
    name?: string;
    email?: string;
    image?: string;
  };
  account?: {
    provider: string;
    type: string;
  };
  profile?: any;
}

// Create auth instance lazily to avoid build-time issues
let authInstance: any = null;

function createAuth() {
  if (!authInstance) {
    const config: any = {
      secret: process.env.BETTER_AUTH_SECRET || 'default-secret-change-in-production',
      baseURL: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
      trustedOrigins: ['http://localhost:3000', 'https://onlyrules.codes'],
      emailAndPassword: {
        enabled: false,
      },
      session: {
        expiresIn: 60 * 60 * 24 * 7, // 7 days
        updateAge: 60 * 60 * 24, // 1 day
      },
      socialProviders: {
        ...(process.env.GITHUB_CLIENT_ID && process.env.GITHUB_CLIENT_SECRET
          ? {
              github: {
                clientId: process.env.GITHUB_CLIENT_ID,
                clientSecret: process.env.GITHUB_CLIENT_SECRET,
                scope: ['read:user', 'user:email'],
              },
            }
          : {}),
      },
      callbacks: {
        async signIn({ user, account, profile }: SignInParams) {
          // Handle users without email
          if (!user.email && account?.provider === 'github') {
            console.log('GitHub user without email:', { user, profile });

            // Try to get email from profile if available
            if (profile && (profile as any).email) {
              user.email = (profile as any).email;
            } else {
              // Create a placeholder email as fallback
              const sanitizedName = user.name?.toLowerCase().replace(/[^a-z0-9]/g, '.') || 'user';
              user.email = `${sanitizedName}.${Date.now()}@github.user`;
              console.log('Created placeholder email:', user.email);
            }
          }
          return true;
        },
      },
    };

    // Only add database adapter if prisma is available
    if (prisma) {
      config.database = prismaAdapter(prisma, {
        provider: 'postgresql',
      });
    }

    authInstance = betterAuth(config);
  }
  return authInstance;
}

export const auth = new Proxy({} as any, {
  get(_target, prop) {
    return createAuth()[prop];
  },
});

// Export a getter function that returns the actual auth instance
export const getAuthInstance = () => createAuth();

export type Session = typeof auth.$Infer.Session;
export type User = (typeof auth.$Infer.Session)['user'];
