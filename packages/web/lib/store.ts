'use client';

import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';

export type IDEType =
  | 'CURSOR'
  | 'AUGMENT'
  | 'WINDSURF'
  | 'CLAUDE'
  | 'GITHUB_COPILOT'
  | 'GEMINI'
  | 'OPENAI_CODEX'
  | 'CLINE'
  | 'JUNIE'
  | 'TRAE'
  | 'LINGMA'
  | 'KIRO'
  | 'TENCENT_CODEBUDDY'
  | 'GENERAL';
export type VisibilityType = 'PRIVATE' | 'PUBLIC';
export type ApplyType = 'auto' | 'manual' | 'always';

export interface RuleSection {
  id: string;
  title: string;
  content: string;
  description?: string;
  name?: string;
  globs?: string;
  applyType?: ApplyType;
}

export interface Rule {
  id: string;
  title: string;
  description?: string | null;
  content: string;
  visibility: VisibilityType;
  applyType: ApplyType;
  glob?: string;
  shareToken?: string | null;
  createdAt: string;
  updatedAt: string;
  userId: string;
  tags: { tag: { id: string; name: string; color: string } }[];
  user?: {
    id: string;
    name: string | null;
    email: string;
    image: string | null;
    avatar: string | null;
  };
}

export interface Tag {
  id: string;
  name: string;
  color: string;
}

export interface RulePayload {
  id?: string;
  title: string;
  description?: string | null;
  content: string;
  visibility: VisibilityType;
  applyType: ApplyType;
  glob?: string;
  shareToken?: string | null;
  createdAt?: string;
  updatedAt?: string;
  userId?: string;
  tags: string[];
}

// IDE Preference Management
export interface IDEPreference {
  id: string;
  name: string;
  type: IDEType;
  isDefault: boolean;
  addedAt: string;
}

export interface IDEPreferences {
  preferredIDEs: IDEPreference[];
}

// Apply Type Display Names and Metadata
export const APPLY_TYPE_METADATA: Record<
  ApplyType,
  { name: string; description: string; color: string }
> = {
  auto: {
    name: 'Auto',
    description: 'Rule is automatically applied when conditions are met',
    color: 'green',
  },
  manual: {
    name: 'Manual',
    description: 'Rule requires manual user action to apply',
    color: 'blue',
  },
  always: {
    name: 'Always',
    description: 'Rule is always applied regardless of context',
    color: 'orange',
  },
};

// IDE Display Names and Metadata
export const IDE_METADATA: Record<IDEType, { name: string; description: string; command: string }> =
  {
    CURSOR: { name: 'Cursor', description: 'AI-powered code editor', command: 'cursor' },
    AUGMENT: { name: 'Augment', description: 'AI coding assistant', command: 'augment' },
    WINDSURF: { name: 'Windsurf', description: 'AI development environment', command: 'windsurf' },
    CLAUDE: { name: 'Claude', description: 'Anthropic AI assistant', command: 'claude' },
    GITHUB_COPILOT: {
      name: 'GitHub Copilot',
      description: 'AI pair programmer',
      command: 'github-copilot',
    },
    GEMINI: { name: 'Gemini', description: 'Google AI assistant', command: 'gemini' },
    OPENAI_CODEX: {
      name: 'OpenAI Codex',
      description: 'OpenAI code assistant',
      command: 'openai-codex',
    },
    CLINE: { name: 'Cline', description: 'AI coding assistant', command: 'cline' },
    JUNIE: { name: 'Junie', description: 'AI development tool', command: 'junie' },
    TRAE: { name: 'Trae', description: 'AI coding companion', command: 'trae' },
    LINGMA: { name: 'Lingma', description: 'AI programming assistant', command: 'lingma' },
    KIRO: { name: 'Kiro', description: 'AI development environment', command: 'kiro' },
    TENCENT_CODEBUDDY: {
      name: 'Tencent CodeBuddy',
      description: 'Tencent AI coding assistant',
      command: 'tencent-codebuddy',
    },
    GENERAL: { name: 'General', description: 'General purpose IDE', command: 'general' },
  };

export const rulesAtom = atom<Rule[]>([]);
export const tagsAtom = atom<Tag[]>([]);
export const selectedRuleAtom = atom<Rule | null>(null);
export const searchQueryAtom = atom<string>('');
export const selectedTagsAtom = atom<string[]>([]);
export const selectedIDEAtom = atom<string>('ALL');
export const themeAtom = atomWithStorage('theme', 'system');

// IDE Preferences Atoms
export const idePreferencesAtom = atomWithStorage<IDEPreferences>('ide-preferences', {
  preferredIDEs: [],
});

// Derived atoms for easier access
export const preferredIDEsAtom = atom(
  (get) => get(idePreferencesAtom).preferredIDEs,
  (get, set, newIDEs: IDEPreference[]) => {
    const current = get(idePreferencesAtom);
    set(idePreferencesAtom, { ...current, preferredIDEs: newIDEs });
  }
);

export const defaultIDEAtom = atom(
  (get) => {
    const preferences = get(idePreferencesAtom);
    return preferences.preferredIDEs.find((ide) => ide.isDefault);
  },
  (get, set, newDefaultId: string | undefined) => {
    const current = get(idePreferencesAtom);
    const updatedIDEs = current.preferredIDEs.map((ide) => ({
      ...ide,
      isDefault: ide.id === newDefaultId,
    }));
    set(idePreferencesAtom, { ...current, preferredIDEs: updatedIDEs });
  }
);
