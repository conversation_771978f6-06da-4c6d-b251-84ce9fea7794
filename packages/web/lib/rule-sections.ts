import type { ApplyType, RuleSection } from './store';

/**
 * Parse rule content with --- delimiters into sections
 */
export function parseRuleContent(content: string): RuleSection[] {
  if (!content.trim()) {
    return [
      {
        id: generateSectionId(),
        title: 'Section 1',
        content: '',
      },
    ];
  }

  // Split by --- delimiter
  const parts = content.split(/^---\s*$/m);

  // If no delimiters found, treat as single section
  if (parts.length === 1) {
    const titleMatch = content.match(/^#\s+(.+)/m);
    const title = titleMatch ? titleMatch[1].trim() : 'Section 1';

    return [
      {
        id: generateSectionId(),
        title,
        content: content.trim(),
      },
    ];
  }

  const sections: RuleSection[] = [];
  let currentMetadata: Partial<RuleSection> = {};

  for (let i = 0; i < parts.length; i++) {
    const part = parts[i].trim();
    if (!part) continue;

    // Check if this part contains only metadata (no content)
    const lines = part.split('\n');
    let isMetadataOnly = true;
    const metadata: Partial<RuleSection> = {};

    for (const line of lines) {
      const trimmedLine = line.trim();
      if (!trimmedLine) continue;

      const metadataMatch = trimmedLine.match(/^(description|name|globs|applyType):\s*(.+)$/);
      if (metadataMatch) {
        const [, key, value] = metadataMatch;
        if (key === 'applyType') {
          const trimmedValue = value.trim();
          if (isValidApplyType(trimmedValue)) {
            metadata[key] = trimmedValue;
          }
        } else {
          metadata[key as keyof RuleSection] = value.trim();
        }
      } else {
        // This line is not metadata, so this part contains content
        isMetadataOnly = false;
        break;
      }
    }

    if (isMetadataOnly && Object.keys(metadata).length > 0) {
      // This part is metadata only, store it for the next content part
      currentMetadata = { ...currentMetadata, ...metadata };
    } else {
      // This part contains content (and possibly metadata)
      let contentBlock = part;
      const sectionMetadata = { ...currentMetadata };

      // If this part starts with metadata, extract it
      const contentLines = lines.slice();
      let contentStartIndex = 0;

      for (let j = 0; j < lines.length; j++) {
        const line = lines[j].trim();
        if (!line) {
          contentStartIndex = j + 1;
          continue;
        }

        const metadataMatch = line.match(/^(description|name|globs|applyType):\s*(.+)$/);
        if (metadataMatch) {
          const [, key, value] = metadataMatch;
          if (key === 'applyType') {
            const trimmedValue = value.trim();
            if (isValidApplyType(trimmedValue)) {
              sectionMetadata[key] = trimmedValue;
            }
          } else {
            sectionMetadata[key as keyof RuleSection] = value.trim();
          }
          contentStartIndex = j + 1;
        } else {
          break;
        }
      }

      contentBlock = contentLines.slice(contentStartIndex).join('\n').trim();

      if (contentBlock) {
        // Extract title from content (first heading)
        const titleMatch = contentBlock.match(/^#\s+(.+)/m);
        const title = titleMatch ? titleMatch[1].trim() : `Section ${sections.length + 1}`;

        sections.push({
          id: generateSectionId(),
          title,
          content: contentBlock,
          ...sectionMetadata,
        });

        // Reset metadata for next section
        currentMetadata = {};
      }
    }
  }

  return sections.length > 0
    ? sections
    : [
        {
          id: generateSectionId(),
          title: 'Section 1',
          content: content.trim(),
        },
      ];
}

/**
 * Convert sections array back to delimited string format
 */
export function formatRuleContent(sections: RuleSection[]): string {
  if (sections.length === 0) {
    return '';
  }

  // If only one section and no metadata, return content directly
  if (
    sections.length === 1 &&
    !sections[0].description &&
    !sections[0].name &&
    !sections[0].globs &&
    !sections[0].applyType
  ) {
    return sections[0].content;
  }

  return sections
    .map((section) => {
      const parts: string[] = [];

      // Add delimiter (except for first section if it has no metadata)
      const hasMetadata = section.description || section.name || section.globs || section.applyType;
      const isFirstSection = sections.indexOf(section) === 0;

      if (!isFirstSection || hasMetadata) {
        parts.push('---');
      }

      // Add metadata
      if (section.description) {
        parts.push(`description: ${section.description}`);
      }
      if (section.name) {
        parts.push(`name: ${section.name}`);
      }
      if (section.globs) {
        parts.push(`globs: ${section.globs}`);
      }
      if (section.applyType) {
        parts.push(`applyType: ${section.applyType}`);
      }

      // Add delimiter after metadata if present
      if (hasMetadata) {
        parts.push('---');
      }

      // Add content
      parts.push('');
      parts.push(section.content);

      return parts.join('\n');
    })
    .join('\n\n');
}

/**
 * Generate a unique section ID
 */
export function generateSectionId(): string {
  return `section_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Create a new empty section
 */
export function createEmptySection(index: number): RuleSection {
  return {
    id: generateSectionId(),
    title: `Section ${index + 1}`,
    content: '',
  };
}

/**
 * Duplicate a section
 */
export function duplicateSection(section: RuleSection): RuleSection {
  return {
    ...section,
    id: generateSectionId(),
    title: `${section.title} (Copy)`,
  };
}

/**
 * Move section in array
 */
export function moveSectionInArray<T>(array: T[], fromIndex: number, toIndex: number): T[] {
  const newArray = [...array];
  const [removed] = newArray.splice(fromIndex, 1);
  newArray.splice(toIndex, 0, removed);
  return newArray;
}

/**
 * Validate section data
 */
export function validateSection(section: RuleSection): string[] {
  const errors: string[] = [];

  if (!section.title.trim()) {
    errors.push('Section title is required');
  }

  if (!section.content.trim()) {
    errors.push('Section content is required');
  }

  return errors;
}

/**
 * Get section summary for display
 */
export function getSectionSummary(section: RuleSection): string {
  const contentPreview = section.content.slice(0, 100).replace(/\n/g, ' ');
  return contentPreview.length > 100 ? `${contentPreview}...` : contentPreview;
}

/**
 * Update a specific section's apply type
 */
export function updateSectionApplyType(
  sections: RuleSection[],
  sectionId: string,
  applyType: ApplyType | undefined
): RuleSection[] {
  return sections.map((section) =>
    section.id === sectionId ? { ...section, applyType } : section
  );
}

/**
 * Validate apply type value
 */
export function isValidApplyType(value: string): value is ApplyType {
  return ['auto', 'manual', 'always'].includes(value);
}
